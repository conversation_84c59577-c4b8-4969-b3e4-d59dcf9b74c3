import os
import torch
import numpy as np
from PIL import Image
from pathlib import Path
from typing import Optional, Tuple, List, Dict, Any
import argparse
from tqdm import tqdm
import einops

# Import required modules from the original code
from diffusers import AutoencoderKLHunyuanVideo
from transformers import LlamaModel, CLIPTextModel, LlamaTokenizerFast, CLIPTokenizer
from diffusers_helper.hunyuan import encode_prompt_conds, vae_decode, vae_encode, vae_decode_fake
from diffusers_helper.utils import save_bcthw_as_mp4, crop_or_pad_yield_mask, soft_append_bcthw, resize_and_center_crop, generate_timestamp
from diffusers_helper.models.hunyuan_video_packed import HunyuanVideoTransformer3DModelPacked
from diffusers_helper.pipelines.k_diffusion_hunyuan import sample_hunyuan
from diffusers_helper.memory import get_cuda_free_memory_gb, unload_complete_models, load_model_as_complete, \
    move_model_to_device_with_memory_preservation, offload_model_from_device_for_memory_preservation, fake_diffusers_current_device
from transformers import SiglipImageProcessor, SiglipVisionModel
from diffusers_helper.clip_vision import hf_clip_vision_encode
from diffusers_helper.bucket_tools import find_nearest_bucket

class HunyuanVideoGenerator:
    def __init__(self, output_dir: str = "./output", device: str = "cuda",
                 high_vram: bool = None, models_dir: str = None):
        """
        Initialize the Hunyuan Video Generator.

        Args:
            output_dir: Directory to save generated videos
            device: Device to run the model on ('cuda' or 'cpu')
            high_vram: Whether to use high VRAM mode (auto-detected if None)
            models_dir: Directory to store downloaded models (defaults to ./models in script directory)
        """
        # Validate device
        if device == "cuda" and not torch.cuda.is_available():
            print("CUDA requested but not available, falling back to CPU")
            device = "cpu"

        self.device = torch.device(device)
        print(f"Using device: {self.device}")
        
        # Set up output directory
        self.output_dir = Path(output_dir).resolve()
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up models directory
        if models_dir is None:
            # Default to 'models' in the same directory as this script
            self.models_dir = Path(__file__).parent / 'models'
        else:
            self.models_dir = Path(models_dir).resolve()
        self.models_dir.mkdir(parents=True, exist_ok=True)
        print(f"Using models directory: {self.models_dir}")
        
        # Auto-detect high VRAM mode if not specified
        if high_vram is None:
            if self.device.type == "cuda":
                free_mem_gb = get_cuda_free_memory_gb(self.device.index or 0)
                self.high_vram = free_mem_gb > 60
                print(f"Detected {'high' if self.high_vram else 'low'} VRAM mode (Free: {free_mem_gb:.1f} GB)")
            else:
                self.high_vram = False
                print("CPU mode detected, using low VRAM settings")
        else:
            self.high_vram = high_vram
        
        # Initialize models as None, will be loaded when needed
        self.text_encoder = None
        self.text_encoder_2 = None
        self.tokenizer = None
        self.tokenizer_2 = None
        self.vae = None
        self.feature_extractor = None
        self.image_encoder = None
        self.transformer = None
        
        # Model paths
        self.model_path = "hunyuanvideo-community/HunyuanVideo"
        self.frame_pack_model_path = "lllyasviel/FramePackI2V_HY"
        self.flux_redux_path = "lllyasviel/flux_redux_bfl"
    
    def load_models(self):
        """Load all required models."""
        print(f"Loading models to {self.models_dir}...")
        
        # Ensure the model directories exist
        model_cache_dir = self.models_dir / "hunyuanvideo-community_HunyuanVideo"
        frame_pack_cache_dir = self.models_dir / "lllyasviel_FramePackI2V_HY"
        flux_redux_cache_dir = self.models_dir / "lllyasviel_flux_redux_bfl"
        
        for dir_path in [model_cache_dir, frame_pack_cache_dir, flux_redux_cache_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # Text encoders and tokenizers
        self.text_encoder = LlamaModel.from_pretrained(
            self.model_path,
            subfolder='text_encoder',
            torch_dtype=torch.float16,
            cache_dir=str(model_cache_dir)
        ).to(self.device)
        
        self.text_encoder_2 = CLIPTextModel.from_pretrained(
            self.model_path,
            subfolder='text_encoder_2',
            torch_dtype=torch.float16,
            cache_dir=str(model_cache_dir)
        ).to(self.device)
        
        self.tokenizer = LlamaTokenizerFast.from_pretrained(
            self.model_path,
            subfolder='tokenizer',
            cache_dir=str(model_cache_dir)
        )
        
        self.tokenizer_2 = CLIPTokenizer.from_pretrained(
            self.model_path,
            subfolder='tokenizer_2',
            cache_dir=str(model_cache_dir)
        )
        
        # VAE
        self.vae = AutoencoderKLHunyuanVideo.from_pretrained(
            self.model_path,
            subfolder='vae',
            torch_dtype=torch.float16,
            cache_dir=str(model_cache_dir)
        ).to(self.device)
        
        # Image encoder for CLIP
        self.feature_extractor = SiglipImageProcessor.from_pretrained(
            self.flux_redux_path,
            subfolder='feature_extractor',
            cache_dir=str(flux_redux_cache_dir)
        )
        
        self.image_encoder = SiglipVisionModel.from_pretrained(
            self.flux_redux_path,
            subfolder='image_encoder',
            torch_dtype=torch.float16,
            cache_dir=str(flux_redux_cache_dir)
        ).to(self.device)
        
        # Transformer model - load to CPU first to avoid device mismatch
        self.transformer = HunyuanVideoTransformer3DModelPacked.from_pretrained(
            self.frame_pack_model_path,
            torch_dtype=torch.bfloat16,
            cache_dir=str(frame_pack_cache_dir)
        )
        
        # Move all models to device with proper data types
        # First ensure all models are on CPU with correct dtypes
        self.vae = self.vae.to(device='cpu', dtype=torch.float16)
        self.text_encoder = self.text_encoder.to(device='cpu', dtype=torch.float16)
        self.text_encoder_2 = self.text_encoder_2.to(device='cpu', dtype=torch.float16)
        self.image_encoder = self.image_encoder.to(device='cpu', dtype=torch.float16)
        
        # Special handling for transformer to ensure bfloat16 consistency
        self.transformer = self.transformer.to(device='cpu')
        for param in self.transformer.parameters():
            param.data = param.data.to(dtype=torch.bfloat16)
            if param._grad is not None:
                param._grad.data = param._grad.data.to(dtype=torch.bfloat16)
        
        # Clear CUDA cache to free up memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
        # Move to target device one by one with proper dtype preservation
        self.vae = self.vae.to(device=self.device)
        self.text_encoder = self.text_encoder.to(device=self.device)
        self.text_encoder_2 = self.text_encoder_2.to(device=self.device)
        self.image_encoder = self.image_encoder.to(device=self.device)
        
        # Move transformer with explicit device and dtype
        self.transformer = self.transformer.to(device=self.device, dtype=torch.bfloat16)
        
        # Ensure all parameters are on the correct device and dtype
        models = {
            'vae': (self.vae, torch.float16),
            'text_encoder': (self.text_encoder, torch.float16),
            'text_encoder_2': (self.text_encoder_2, torch.float16),
            'image_encoder': (self.image_encoder, torch.float16),
            'transformer': (self.transformer, torch.bfloat16)
        }
        
        for name, (model, dtype) in models.items():
            for param in model.parameters():
                if param.device != self.device or param.dtype != dtype:
                    param.data = param.data.to(device=self.device, dtype=dtype)
                if param._grad is not None and (param._grad.device != self.device or param._grad.dtype != dtype):
                    param._grad.data = param._grad.data.to(device=self.device, dtype=dtype)
        
        # Set model modes
        self.vae.eval()
        self.text_encoder.eval()
        self.text_encoder_2.eval()
        self.image_encoder.eval()
        self.transformer.eval()
        
        # Configure models based on VRAM
        if not self.high_vram:
            self.vae.enable_slicing()
            self.vae.enable_tiling()
        
        # Set high quality output
        self.transformer.high_quality_fp32_output_for_inference = True
        
        print("All models loaded successfully.")
    
    def unload_models(self):
        """Unload models to free up memory."""
        if not self.high_vram:
            unload_complete_models(
                self.text_encoder, 
                self.text_encoder_2, 
                self.image_encoder, 
                self.vae, 
                self.transformer
            )
    
    def generate_video(
        self,
        input_image: np.ndarray,
        prompt: str,
        n_prompt: str = "",
        seed: int = 42,
        total_second_length: float = 4.0,
        latent_window_size: int = 16,
        steps: int = 50,
        cfg: float = 3.0,
        gs: float = 1.0,
        rs: float = 0.0,
        gpu_memory_preservation: float = 8.0,
        use_teacache: bool = True,
        mp4_crf: int = 18,
        progress_callback=None
    ) -> str:
        """
        Generate a video from an input image and prompt.
        
        Args:
            input_image: Input image as a numpy array (H, W, C)
            prompt: Text prompt for video generation
            n_prompt: Negative prompt
            seed: Random seed
            total_second_length: Desired video length in seconds
            latent_window_size: Size of the latent window
            steps: Number of denoising steps
            cfg: Classifier-free guidance scale
            gs: Distilled guidance scale
            rs: Guidance rescale
            gpu_memory_preservation: GB of GPU memory to preserve
            use_teacache: Whether to use teacher forcing cache
            mp4_crf: CRF value for MP4 encoding (lower = better quality)
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Path to the generated video file
        """
        # Set random seed
        torch.manual_seed(seed)
        
        # Calculate total latent sections
        total_latent_sections = int(max(round((total_second_length * 30) / (latent_window_size * 4)), 1))
        job_id = generate_timestamp()
        
        # Create output directory
        output_path = self.output_dir / f"{job_id}"
        output_path.mkdir(exist_ok=True)
        
        try:
            # Clean GPU if not in high VRAM mode
            if not self.high_vram:
                unload_complete_models(
                    self.text_encoder, 
                    self.text_encoder_2, 
                    self.image_encoder, 
                    self.vae, 
                    self.transformer
                )
            
            # Encode text prompts
            self._update_progress(progress_callback, 0, "Encoding text prompts...")
            
            if not self.high_vram:
                fake_diffusers_current_device(self.text_encoder, self.device)
                load_model_as_complete(self.text_encoder_2, target_device=self.device)
            
            # Ensure models are on the correct device before encoding
            self.text_encoder = self.text_encoder.to(self.device)
            self.text_encoder_2 = self.text_encoder_2.to(self.device)
            
            llama_vec, clip_l_pooler = encode_prompt_conds(
                prompt, 
                self.text_encoder, 
                self.text_encoder_2, 
                self.tokenizer, 
                self.tokenizer_2
            )
            
            if cfg == 1:
                llama_vec_n, clip_l_pooler_n = torch.zeros_like(llama_vec), torch.zeros_like(clip_l_pooler)
            else:
                llama_vec_n, clip_l_pooler_n = encode_prompt_conds(
                    n_prompt, 
                    self.text_encoder, 
                    self.text_encoder_2, 
                    self.tokenizer, 
                    self.tokenizer_2
                )
            
            llama_vec, llama_attention_mask = crop_or_pad_yield_mask(llama_vec, length=512)
            llama_vec_n, llama_attention_mask_n = crop_or_pad_yield_mask(llama_vec_n, length=512)
            
            # Move tensors to the correct device
            llama_vec = llama_vec.to(self.device)
            clip_l_pooler = clip_l_pooler.to(self.device)
            llama_vec_n = llama_vec_n.to(self.device)
            clip_l_pooler_n = clip_l_pooler_n.to(self.device)
            llama_attention_mask = llama_attention_mask.to(self.device)
            llama_attention_mask_n = llama_attention_mask_n.to(self.device)
            
            # Process input image
            self._update_progress(progress_callback, 10, "Processing input image...")
            
            H, W, C = input_image.shape
            height, width = find_nearest_bucket(H, W, resolution=640)
            input_image_np = resize_and_center_crop(input_image, target_width=width, target_height=height)
            
            # Save the processed input image
            input_image_path = output_path / "input.png"
            Image.fromarray(input_image_np).save(input_image_path)
            
            # Prepare image tensor
            input_image_pt = torch.from_numpy(input_image_np).float() / 127.5 - 1
            input_image_pt = input_image_pt.permute(2, 0, 1)[None, :, None]
            
            # Encode image with VAE
            self._update_progress(progress_callback, 20, "Encoding image with VAE...")
            
            if not self.high_vram:
                load_model_as_complete(self.vae, target_device=self.device)
            
            start_latent = vae_encode(input_image_pt, self.vae)
            
            # Encode with CLIP Vision
            self._update_progress(progress_callback, 30, "Encoding with CLIP Vision...")
            
            if not self.high_vram:
                load_model_as_complete(self.image_encoder, target_device=self.device)
            
            image_encoder_output = hf_clip_vision_encode(input_image_np, self.feature_extractor, self.image_encoder)
            image_encoder_last_hidden_state = image_encoder_output.last_hidden_state
            
            # Ensure transformer is on the correct device and convert to correct dtypes
            self.transformer = self.transformer.to(self.device)
            llama_vec = llama_vec.to(self.transformer.dtype)
            llama_vec_n = llama_vec_n.to(self.transformer.dtype)
            clip_l_pooler = clip_l_pooler.to(self.transformer.dtype)
            clip_l_pooler_n = clip_l_pooler_n.to(self.transformer.dtype)
            image_encoder_last_hidden_state = image_encoder_last_hidden_state.to(self.transformer.dtype).to(self.device)
            
            # Initialize sampling
            self._update_progress(progress_callback, 40, "Starting video generation...")
            
            rnd = torch.Generator("cpu").manual_seed(seed)
            num_frames = latent_window_size * 4 - 3
            
            history_latents = torch.zeros(size=(1, 16, 1 + 2 + 16, height // 8, width // 8), dtype=torch.float32).cpu()
            history_pixels = None
            total_generated_latent_frames = 0
            
            # Determine latent paddings
            latent_paddings = list(reversed(range(total_latent_sections)))
            if total_latent_sections > 4:
                latent_paddings = [3] + [2] * (total_latent_sections - 3) + [1, 0]
            
            # Generate video in sections
            for i, latent_padding in enumerate(latent_paddings):
                is_last_section = latent_padding == 0
                latent_padding_size = latent_padding * latent_window_size
                
                print(f'latent_padding_size = {latent_padding_size}, is_last_section = {is_last_section}')
                
                # Prepare indices for the current section
                indices = torch.arange(0, sum([1, latent_padding_size, latent_window_size, 1, 2, 16])).unsqueeze(0)
                clean_latent_indices_pre, blank_indices, latent_indices, clean_latent_indices_post, clean_latent_2x_indices, clean_latent_4x_indices = indices.split(
                    [1, latent_padding_size, latent_window_size, 1, 2, 16], dim=1)
                clean_latent_indices = torch.cat([clean_latent_indices_pre, clean_latent_indices_post], dim=1)
                
                # Prepare latents for the current section
                clean_latents_pre = start_latent.to(history_latents)
                clean_latents_post, clean_latents_2x, clean_latents_4x = history_latents[:, :, :1 + 2 + 16, :, :].split([1, 2, 16], dim=2)
                clean_latents = torch.cat([clean_latents_pre, clean_latents_post], dim=2)
                
                # Move models to GPU if needed
                if not self.high_vram:
                    unload_complete_models()
                    move_model_to_device_with_memory_preservation(
                        self.transformer,
                        target_device=self.device,
                        preserved_memory_gb=gpu_memory_preservation
                    )
                
                # Configure teacher forcing cache
                if use_teacache:
                    self.transformer.initialize_teacache(enable_teacache=True, num_steps=steps)
                else:
                    self.transformer.initialize_teacache(enable_teacache=False)
                
                # Callback for progress updates
                def callback(d):
                    if progress_callback is not None:
                        preview = d['denoised']
                        preview = vae_decode_fake(preview)
                        preview = (preview * 255.0).detach().cpu().numpy().clip(0, 255).astype(np.uint8)
                        preview = einops.rearrange(preview, 'b c t h w -> (b h) (t w) c')
                        
                        current_step = d['i'] + 1
                        percentage = 40 + int(50.0 * current_step / steps)
                        hint = f'Sampling {current_step}/{steps}'
                        desc = f'Total generated frames: {int(max(0, total_generated_latent_frames * 4 - 3))}, Video length: {max(0, (total_generated_latent_frames * 4 - 3) / 30) :.2f} seconds (FPS-30). The video is being extended now...'
                        
                        progress_callback(percentage, desc, preview)
                
                # Generate latents for the current section
                generated_latents = sample_hunyuan(
                    transformer=self.transformer,
                    sampler='unipc',
                    width=width,
                    height=height,
                    frames=num_frames,
                    real_guidance_scale=cfg,
                    distilled_guidance_scale=gs,
                    guidance_rescale=rs,
                    num_inference_steps=steps,
                    generator=rnd,
                    prompt_embeds=llama_vec,
                    prompt_embeds_mask=llama_attention_mask,
                    prompt_poolers=clip_l_pooler,
                    negative_prompt_embeds=llama_vec_n,
                    negative_prompt_embeds_mask=llama_attention_mask_n,
                    negative_prompt_poolers=clip_l_pooler_n,
                    device=self.device,
                    dtype=torch.bfloat16,
                    image_embeddings=image_encoder_last_hidden_state,
                    latent_indices=latent_indices,
                    clean_latents=clean_latents,
                    clean_latent_indices=clean_latent_indices,
                    clean_latents_2x=clean_latents_2x,
                    clean_latent_2x_indices=clean_latent_2x_indices,
                    clean_latents_4x=clean_latents_4x,
                    clean_latent_4x_indices=clean_latent_4x_indices,
                    callback=callback,
                )
                
                # Process generated latents
                if is_last_section:
                    generated_latents = torch.cat([start_latent.to(generated_latents), generated_latents], dim=2)
                
                total_generated_latent_frames += int(generated_latents.shape[2])
                history_latents = torch.cat([generated_latents.to(history_latents), history_latents], dim=2)
                
                # Decode latents to pixels
                if not self.high_vram:
                    offload_model_from_device_for_memory_preservation(
                        self.transformer,
                        target_device=self.device,
                        preserved_memory_gb=8
                    )
                    load_model_as_complete(self.vae, target_device=self.device)
                
                real_history_latents = history_latents[:, :, :total_generated_latent_frames, :, :]
                
                if history_pixels is None:
                    history_pixels = vae_decode(real_history_latents, self.vae).cpu()
                else:
                    section_latent_frames = (latent_window_size * 2 + 1) if is_last_section else (latent_window_size * 2)
                    overlapped_frames = latent_window_size * 4 - 3
                    
                    current_pixels = vae_decode(real_history_latents[:, :, :section_latent_frames], self.vae).cpu()
                    history_pixels = soft_append_bcthw(current_pixels, history_pixels, overlapped_frames)
                
                if not self.high_vram:
                    unload_complete_models()
                
                # Save intermediate video
                output_filename = str(output_path / f"output_{total_generated_latent_frames}.mp4")
                save_bcthw_as_mp4(history_pixels, output_filename, fps=30, crf=mp4_crf)
                
                print(f'Decoded. Current latent shape {real_history_latents.shape}; pixel shape {history_pixels.shape}')
                
                if progress_callback:
                    progress_callback(
                        90 + int(10 * (i + 1) / len(latent_paddings)),
                        f"Generated section {i+1}/{len(latent_paddings)}"
                    )
                
                if is_last_section:
                    break
            
            # Save final video
            final_output_path = output_path / "final_output.mp4"
            save_bcthw_as_mp4(history_pixels, str(final_output_path), fps=30, crf=mp4_crf)
            
            self._update_progress(progress_callback, 100, "Video generation complete!")
            
            return str(final_output_path)
            
        except Exception as e:
            print(f"Error during video generation: {str(e)}")
            raise
        finally:
            self.unload_models()
    
    def _update_progress(self, callback, percentage: int, message: str, preview=None):
        """Helper method to update progress through the callback."""
        if callback is not None:
            callback(percentage, message, preview)

def main():
    """Command-line interface for the video generator."""
    parser = argparse.ArgumentParser(description="Generate videos using Hunyuan Video model")
    
    # Required arguments
    parser.add_argument("input_image", type=str, help="Path to input image")
    parser.add_argument("prompt", type=str, help="Text prompt for video generation")
    
    # Optional arguments
    parser.add_argument("--negative_prompt", type=str, default="", help="Negative prompt")
    parser.add_argument("--output_dir", type=str, default="./output", help="Output directory")
    parser.add_argument("--seed", type=int, default=42, help="Random seed")
    parser.add_argument("--duration", type=float, default=4.0, help="Video duration in seconds")
    parser.add_argument("--latent_window", type=int, default=16, help="Latent window size")
    parser.add_argument("--steps", type=int, default=50, help="Number of denoising steps")
    parser.add_argument("--cfg", type=float, default=3.0, help="Classifier-free guidance scale")
    parser.add_argument("--gs", type=float, default=1.0, help="Distilled guidance scale")
    parser.add_argument("--rs", type=float, default=0.0, help="Guidance rescale")
    parser.add_argument("--gpu_memory", type=float, default=8.0, help="GPU memory to preserve in GB")
    parser.add_argument("--no_teacache", action="store_false", dest="use_teacache", help="Disable teacher forcing cache")
    parser.add_argument("--crf", type=int, default=18, help="CRF value for MP4 encoding (lower = better quality)")
    parser.add_argument("--device", type=str, default="cuda", help="Device to run on ('cuda' or 'cpu')")
    parser.add_argument("--high_vram", action="store_true", help="Force high VRAM mode")
    
    args = parser.parse_args()
    
    # Load input image
    try:
        input_image = Image.open(args.input_image).convert("RGB")
        input_image = np.array(input_image)
    except Exception as e:
        print(f"Error loading input image: {str(e)}")
        return
    
    # Initialize video generator
    generator = HunyuanVideoGenerator(
        output_dir=args.output_dir,
        device=args.device,
        high_vram=args.high_vram
    )
    
    # Load models
    try:
        generator.load_models()
    except Exception as e:
        print(f"Error loading models: {str(e)}")
        return
    
    # Progress callback
    def progress_callback(percentage, message, preview=None):
        print(f"\r[{percentage}%] {message}", end="", flush=True)
    
    # Generate video
    try:
        print("Starting video generation...")
        output_path = generator.generate_video(
            input_image=input_image,
            prompt=args.prompt,
            n_prompt=args.negative_prompt,
            seed=args.seed,
            total_second_length=args.duration,
            latent_window_size=args.latent_window,
            steps=args.steps,
            cfg=args.cfg,
            gs=args.gs,
            rs=args.rs,
            gpu_memory_preservation=args.gpu_memory,
            use_teacache=args.use_teacache,
            mp4_crf=args.crf,
            progress_callback=progress_callback
        )
        
        print(f"\nVideo generation complete! Output saved to: {output_path}")
        
    except Exception as e:
        print(f"\nError during video generation: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        generator.unload_models()

if __name__ == "__main__":
    main()
